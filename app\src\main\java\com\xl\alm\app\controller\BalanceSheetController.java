package com.xl.alm.app.controller;

import com.jd.lightning.common.annotation.Log;
import com.jd.lightning.common.core.controller.BaseController;
import com.jd.lightning.common.core.domain.Result;
import com.jd.lightning.common.core.page.TableDataInfo;
import com.jd.lightning.common.enums.BusinessType;
import com.xl.alm.app.util.ExcelUtil;
import com.xl.alm.app.dto.BalanceSheetDTO;
import com.xl.alm.app.query.BalanceSheetQuery;
import com.xl.alm.app.service.BalanceSheetService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 资产负债表 Controller
 *
 * <AUTHOR> Assistant
 */
@RestController
@RequestMapping("/liab/balance/sheet")
public class BalanceSheetController extends BaseController {

    @Autowired
    private BalanceSheetService balanceSheetService;

    /**
     * 查询资产负债表列表
     */
    @PreAuthorize("@ss.hasPermi('liab:balance:sheet:list')")
    @GetMapping("/list")
    public TableDataInfo list(BalanceSheetQuery balanceSheetQuery) {
        startPage();
        List<BalanceSheetDTO> list = balanceSheetService.selectBalanceSheetDtoList(balanceSheetQuery);
        return getDataTable(list);
    }

    /**
     * 获取资产负债表详细信息
     */
    @PreAuthorize("@ss.hasPermi('liab:balance:sheet:query')")
    @GetMapping(value = "/{id}")
    public Result getInfo(@PathVariable("id") Long id) {
        return Result.success(balanceSheetService.selectBalanceSheetDtoById(id));
    }

    /**
     * 新增资产负债表
     */
    @PreAuthorize("@ss.hasPermi('liab:balance:sheet:add')")
    @Log(title = "资产负债表", businessType = BusinessType.INSERT)
    @PostMapping
    public Result add(@RequestBody BalanceSheetDTO balanceSheetDTO) {
        return toAjax(balanceSheetService.addBalanceSheetDto(balanceSheetDTO));
    }

    /**
     * 修改资产负债表
     */
    @PreAuthorize("@ss.hasPermi('liab:balance:sheet:edit')")
    @Log(title = "资产负债表", businessType = BusinessType.UPDATE)
    @PutMapping
    public Result edit(@RequestBody BalanceSheetDTO balanceSheetDTO) {
        return toAjax(balanceSheetService.updateBalanceSheetDto(balanceSheetDTO));
    }

    /**
     * 删除资产负债表
     */
    @PreAuthorize("@ss.hasPermi('liab:balance:sheet:remove')")
    @Log(title = "资产负债表", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public Result remove(@PathVariable Long[] ids) {
        return toAjax(balanceSheetService.deleteBalanceSheetDtoByIds(ids));
    }

    /**
     * 导出资产负债表
     */
    @PreAuthorize("@ss.hasPermi('liab:balance:sheet:export')")
    @Log(title = "资产负债表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BalanceSheetQuery query) {
        List<BalanceSheetDTO> list = balanceSheetService.selectBalanceSheetDtoList(query);
        ExcelUtil<BalanceSheetDTO> util = new ExcelUtil<>(BalanceSheetDTO.class);
        util.exportExcel(list, "资产负债表数据", response);
    }

    /**
     * 获取资产负债表导入模板
     */
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<BalanceSheetDTO> util = new ExcelUtil<>(BalanceSheetDTO.class);
        util.exportTemplateExcel(response, "资产负债表");
    }

    /**
     * 导入资产负债表数据
     */
    @PreAuthorize("@ss.hasPermi('liab:balance:sheet:import')")
    @Log(title = "资产负债表", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public Result importData(MultipartFile file, boolean updateSupport) throws Exception {
        ExcelUtil<BalanceSheetDTO> util = new ExcelUtil<>(BalanceSheetDTO.class);
        List<BalanceSheetDTO> balanceSheetList = util.importExcel(file.getInputStream());
        String operName = getUsername();
        String message = balanceSheetService.importBalanceSheetDto(balanceSheetList, updateSupport, operName);
        return Result.success(message);
    }
}
