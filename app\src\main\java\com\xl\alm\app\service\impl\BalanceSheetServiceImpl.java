package com.xl.alm.app.service.impl;

import com.jd.lightning.common.utils.StringUtils;
import com.xl.alm.app.dto.BalanceSheetDTO;
import com.xl.alm.app.entity.BalanceSheetEntity;
import com.xl.alm.app.mapper.BalanceSheetMapper;
import com.xl.alm.app.query.BalanceSheetQuery;
import com.xl.alm.app.service.BalanceSheetService;
import com.xl.alm.app.util.EntityDtoConvertUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 资产负债表 Service 实现类
 *
 * <AUTHOR> Assistant
 */
@Slf4j
@Service
public class BalanceSheetServiceImpl implements BalanceSheetService {

    @Autowired
    private BalanceSheetMapper balanceSheetMapper;

    /**
     * 查询资产负债表列表
     *
     * @param balanceSheetQuery 资产负债表查询条件
     * @return 资产负债表列表
     */
    @Override
    public List<BalanceSheetDTO> selectBalanceSheetDtoList(BalanceSheetQuery balanceSheetQuery) {
        List<BalanceSheetEntity> entityList = balanceSheetMapper.selectBalanceSheetEntityList(balanceSheetQuery);
        if (entityList == null || entityList.isEmpty()) {
            return null;
        }
        return EntityDtoConvertUtil.convertToDTOList(entityList, BalanceSheetDTO.class);
    }

    /**
     * 用id查询资产负债表
     *
     * @param id id
     * @return 资产负债表
     */
    @Override
    public BalanceSheetDTO selectBalanceSheetDtoById(Long id) {
        BalanceSheetEntity entity = balanceSheetMapper.selectBalanceSheetEntityById(id);
        if (entity == null) {
            return null;
        }
        return EntityDtoConvertUtil.convertToDTO(entity, BalanceSheetDTO.class);
    }

    /**
     * 根据账期、类别和项目名称查询资产负债表
     *
     * @param accountingPeriod 账期
     * @param category         类别
     * @param itemName         项目名称
     * @return 资产负债表
     */
    @Override
    public BalanceSheetDTO selectBalanceSheetDtoByCondition(
            String accountingPeriod,
            String category,
            String itemName) {
        BalanceSheetEntity entity = balanceSheetMapper.selectBalanceSheetEntityByCondition(
                accountingPeriod,
                category,
                itemName);
        if (entity == null) {
            return null;
        }
        return EntityDtoConvertUtil.convertToDTO(entity, BalanceSheetDTO.class);
    }

    /**
     * 新增资产负债表
     *
     * @param dto 资产负债表
     * @return 结果
     */
    @Override
    public int addBalanceSheetDto(BalanceSheetDTO dto) {
        BalanceSheetEntity entity = EntityDtoConvertUtil.convertToEntity(dto, BalanceSheetEntity.class);
        return balanceSheetMapper.insertBalanceSheetEntity(entity);
    }

    /**
     * 修改资产负债表
     *
     * @param dto 资产负债表
     * @return 结果
     */
    @Override
    public int updateBalanceSheetDto(BalanceSheetDTO dto) {
        BalanceSheetEntity entity = EntityDtoConvertUtil.convertToEntity(dto, BalanceSheetEntity.class);
        return balanceSheetMapper.updateBalanceSheetEntity(entity);
    }

    /**
     * 批量删除资产负债表
     *
     * @param ids 需要删除的资产负债表主键集合
     * @return 结果
     */
    @Override
    public int deleteBalanceSheetDtoByIds(Long[] ids) {
        return balanceSheetMapper.deleteBalanceSheetEntityByIds(ids);
    }

    /**
     * 删除资产负债表信息
     *
     * @param id 资产负债表主键
     * @return 结果
     */
    @Override
    public int deleteBalanceSheetDtoById(Long id) {
        return balanceSheetMapper.deleteBalanceSheetEntityById(id);
    }

    /**
     * 批量插入资产负债表数据
     *
     * @param balanceSheetDtoList 资产负债表列表
     * @return 影响行数
     */
    @Override
    public int batchInsertBalanceSheetDto(List<BalanceSheetDTO> balanceSheetDtoList) {
        if (balanceSheetDtoList == null || balanceSheetDtoList.isEmpty()) {
            return 0;
        }
        List<BalanceSheetEntity> entityList = EntityDtoConvertUtil.convertToEntityList(balanceSheetDtoList, BalanceSheetEntity.class);
        return balanceSheetMapper.batchInsertBalanceSheetEntity(entityList);
    }

    /**
     * 删除指定账期的资产负债表数据
     *
     * @param accountingPeriod 账期
     * @return 影响行数
     */
    @Override
    public int deleteBalanceSheetDtoByPeriod(String accountingPeriod) {
        return balanceSheetMapper.deleteBalanceSheetEntityByPeriod(accountingPeriod);
    }

    /**
     * 导入资产负债表
     *
     * @param dtoList       资产负债表数据列表
     * @param updateSupport 是否更新支持，如果已存在，是否更新
     * @param username      操作用户
     * @return 结果
     */
    @Override
    public String importBalanceSheetDto(List<BalanceSheetDTO> dtoList, Boolean updateSupport, String username) {
        if (StringUtils.isNull(dtoList) || dtoList.size() == 0) {
            throw new RuntimeException("导入资产负债表数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        for (BalanceSheetDTO dto : dtoList) {
            try {
                // 验证是否存在这个资产负债表
                BalanceSheetDTO existDto = this.selectBalanceSheetDtoByCondition(dto.getAccountingPeriod(), dto.getCategory(), dto.getItemName());
                if (StringUtils.isNull(existDto)) {
                    this.addBalanceSheetDto(dto);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、账期 " + dto.getAccountingPeriod() + " 类别 " + dto.getCategory() + " 项目 " + dto.getItemName() + " 导入成功");
                } else if (updateSupport) {
                    dto.setId(existDto.getId());
                    this.updateBalanceSheetDto(dto);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、账期 " + dto.getAccountingPeriod() + " 类别 " + dto.getCategory() + " 项目 " + dto.getItemName() + " 更新成功");
                } else {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、账期 " + dto.getAccountingPeriod() + " 类别 " + dto.getCategory() + " 项目 " + dto.getItemName() + " 已存在");
                }
            } catch (Exception e) {
                failureNum++;
                String msg = "<br/>" + failureNum + "、账期 " + dto.getAccountingPeriod() + " 类别 " + dto.getCategory() + " 项目 " + dto.getItemName() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
                log.error(msg, e);
            }
        }
        if (failureNum > 0) {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new RuntimeException(failureMsg.toString());
        } else {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }
}
