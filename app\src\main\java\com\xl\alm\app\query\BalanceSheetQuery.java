package com.xl.alm.app.query;

import com.jd.lightning.common.core.domain.BaseQuery;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 资产负债表Query
 *
 * <AUTHOR> Assistant
 */
@Data
public class BalanceSheetQuery extends BaseQuery {
    private Long id;
    
    /**
     * 账期，格式：YYYYMM（如202503）
     */
    private String accountingPeriod;
    
    /**
     * 类别，值域：资产、负债、所有者权益
     */
    private String category;
    
    /**
     * 资产负债表项目名称
     */
    private String itemName;
    
    /**
     * 期末余额金额
     */
    private BigDecimal endingBalance;
    
    /**
     * 年初余额金额
     */
    private BigDecimal beginningBalance;
    
    /**
     * 备注信息
     */
    private String remark;
    
    /**
     * 是否删除，0：否，1：是
     */
    private int isDel = 0;
}
