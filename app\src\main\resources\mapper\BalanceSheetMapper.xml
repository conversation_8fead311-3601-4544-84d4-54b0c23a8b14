<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xl.alm.app.mapper.BalanceSheetMapper">

    <resultMap type="com.xl.alm.app.entity.BalanceSheetEntity" id="BalanceSheetEntityResult">
        <result property="id" column="id"/>
        <result property="accountingPeriod" column="accounting_period"/>
        <result property="category" column="category"/>
        <result property="itemName" column="item_name"/>
        <result property="endingBalance" column="ending_balance"/>
        <result property="beginningBalance" column="beginning_balance"/>
        <result property="remark" column="remark"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDel" column="is_del"/>
    </resultMap>

    <sql id="selectBalanceSheetEntityVo">
        select id, accounting_period, category, item_name, ending_balance, beginning_balance, 
               remark, create_time, create_by, update_time, update_by, is_del
        from t_liab_balance_sheet
    </sql>

    <!-- 查询资产负债表列表 -->
    <select id="selectBalanceSheetEntityList" parameterType="com.xl.alm.app.query.BalanceSheetQuery" resultMap="BalanceSheetEntityResult">
        <include refid="selectBalanceSheetEntityVo"/>
        <where>
            <if test="id != null">and id = #{id}</if>
            <if test="accountingPeriod != null and accountingPeriod != ''">and accounting_period = #{accountingPeriod}</if>
            <if test="category != null and category != ''">and category = #{category}</if>
            <if test="itemName != null and itemName != ''">and item_name like concat('%', #{itemName}, '%')</if>
            <if test="endingBalance != null">and ending_balance = #{endingBalance}</if>
            <if test="beginningBalance != null">and beginning_balance = #{beginningBalance}</if>
            <if test="remark != null and remark != ''">and remark like concat('%', #{remark}, '%')</if>
            and is_del = 0
        </where>
        order by accounting_period desc, category, item_name
    </select>

    <!-- 用id查询资产负债表 -->
    <select id="selectBalanceSheetEntityById" parameterType="Long" resultMap="BalanceSheetEntityResult">
        <include refid="selectBalanceSheetEntityVo"/>
        where id = #{id} and is_del = 0
    </select>

    <!-- 根据账期、类别和项目名称查询资产负债表 -->
    <select id="selectBalanceSheetEntityByCondition" resultMap="BalanceSheetEntityResult">
        <include refid="selectBalanceSheetEntityVo"/>
        <where>
            <if test="accountingPeriod != null and accountingPeriod != ''">and accounting_period = #{accountingPeriod}</if>
            <if test="category != null and category != ''">and category = #{category}</if>
            <if test="itemName != null and itemName != ''">and item_name = #{itemName}</if>
            and is_del = 0
        </where>
        limit 1
    </select>

    <!-- 新增资产负债表 -->
    <insert id="insertBalanceSheetEntity" parameterType="com.xl.alm.app.entity.BalanceSheetEntity" useGeneratedKeys="true" keyProperty="id">
        insert into t_liab_balance_sheet
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="accountingPeriod != null and accountingPeriod != ''">accounting_period,</if>
            <if test="category != null and category != ''">category,</if>
            <if test="itemName != null and itemName != ''">item_name,</if>
            <if test="endingBalance != null">ending_balance,</if>
            <if test="beginningBalance != null">beginning_balance,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="accountingPeriod != null and accountingPeriod != ''">#{accountingPeriod},</if>
            <if test="category != null and category != ''">#{category},</if>
            <if test="itemName != null and itemName != ''">#{itemName},</if>
            <if test="endingBalance != null">#{endingBalance},</if>
            <if test="beginningBalance != null">#{beginningBalance},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <!-- 修改资产负债表 -->
    <update id="updateBalanceSheetEntity" parameterType="com.xl.alm.app.entity.BalanceSheetEntity">
        update t_liab_balance_sheet
        <trim prefix="SET" suffixOverrides=",">
            <if test="accountingPeriod != null and accountingPeriod != ''">accounting_period = #{accountingPeriod},</if>
            <if test="category != null and category != ''">category = #{category},</if>
            <if test="itemName != null and itemName != ''">item_name = #{itemName},</if>
            <if test="endingBalance != null">ending_balance = #{endingBalance},</if>
            <if test="beginningBalance != null">beginning_balance = #{beginningBalance},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <!-- 删除资产负债表 -->
    <update id="deleteBalanceSheetEntityById" parameterType="Long">
        update t_liab_balance_sheet set is_del = 1 where id = #{id}
    </update>

    <!-- 批量删除资产负债表 -->
    <update id="deleteBalanceSheetEntityByIds" parameterType="String">
        update t_liab_balance_sheet set is_del = 1 where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- 批量插入资产负债表数据 -->
    <insert id="batchInsertBalanceSheetEntity" parameterType="java.util.List">
        insert into t_liab_balance_sheet (accounting_period, category, item_name, ending_balance, beginning_balance, remark)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.accountingPeriod}, #{item.category}, #{item.itemName}, #{item.endingBalance}, #{item.beginningBalance}, #{item.remark})
        </foreach>
    </insert>

    <!-- 删除指定账期的资产负债表数据 -->
    <update id="deleteBalanceSheetEntityByPeriod" parameterType="String">
        update t_liab_balance_sheet set is_del = 1 where accounting_period = #{accountingPeriod}
    </update>

</mapper>
