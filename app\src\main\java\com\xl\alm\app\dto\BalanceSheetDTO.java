package com.xl.alm.app.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.jd.lightning.common.annotation.Excel;
import com.jd.lightning.common.core.domain.BaseDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import javax.validation.constraints.DecimalMax;
import javax.validation.constraints.DecimalMin;
import java.math.BigDecimal;

/**
 * 资产负债表DTO
 *
 * <AUTHOR> Assistant
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class BalanceSheetDTO extends BaseDTO {
    private Long id;
    
    /**
     * 账期，格式：YYYYMM（如202503）
     */
    @NotBlank(message = "账期不能为空")
    @Pattern(regexp = "^\\d{6}$", message = "账期格式必须为YYYYMM")
    @Excel(name = "账期")
    @ExcelProperty("账期")
    private String accountingPeriod;
    
    /**
     * 类别，值域：资产、负债、所有者权益
     */
    @NotBlank(message = "类别不能为空")
    @Size(max = 20, message = "类别长度不能超过20个字符")
    @Excel(name = "类别", dictType = "liab_balance_category")
    @ExcelProperty("类别")
    private String category;
    
    /**
     * 资产负债表项目名称
     */
    @NotBlank(message = "项目名称不能为空")
    @Size(max = 100, message = "项目名称长度不能超过100个字符")
    @Excel(name = "项目名称")
    @ExcelProperty("项目名称")
    private String itemName;
    
    /**
     * 期末余额金额
     */
    @DecimalMax(value = "********************.**********", message = "期末余额金额不能超过最大值")
    @DecimalMin(value = "-********************.**********", message = "期末余额金额不能小于最小值")
    @Excel(name = "期末余额金额")
    @ExcelProperty("期末余额金额")
    private BigDecimal endingBalance;
    
    /**
     * 年初余额金额
     */
    @DecimalMax(value = "********************.**********", message = "年初余额金额不能超过最大值")
    @DecimalMin(value = "-********************.**********", message = "年初余额金额不能小于最小值")
    @Excel(name = "年初余额金额")
    @ExcelProperty("年初余额金额")
    private BigDecimal beginningBalance;
    
    /**
     * 备注信息
     */
    @Size(max = 500, message = "备注信息长度不能超过500个字符")
    @Excel(name = "备注信息")
    @ExcelProperty("备注信息")
    private String remark;
    
    /**
     * 是否删除，0：否，1：是
     */
    private int isDel = 0;
}
