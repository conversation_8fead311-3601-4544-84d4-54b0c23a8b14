package com.xl.alm.app.util;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.converters.ReadConverterContext;
import com.alibaba.excel.converters.WriteConverterContext;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.jd.lightning.common.utils.StringUtils;

import java.math.BigDecimal;

/**
 * 资产负债表专用BigDecimal转换器
 * 解决零值科学计数法和尾随零问题
 *
 * <AUTHOR> Assistant
 */
public class BalanceSheetBigDecimalConverter implements Converter<BigDecimal> {

    @Override
    public Class<?> supportJavaTypeKey() {
        return BigDecimal.class;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return CellDataTypeEnum.STRING;
    }

    @Override
    public BigDecimal convertToJavaData(ReadConverterContext<?> context) {
        ReadCellData<?> cellData = context.getReadCellData();
        if (cellData == null) {
            return null;
        }

        String stringValue = cellData.getStringValue();
        if (StringUtils.isEmpty(stringValue)) {
            return null;
        }

        try {
            return new BigDecimal(stringValue.trim());
        } catch (NumberFormatException e) {
            // 如果转换失败，返回null或抛出异常
            return null;
        }
    }

    @Override
    public WriteCellData<?> convertToExcelData(WriteConverterContext<BigDecimal> context) {
        BigDecimal value = context.getValue();
        if (value == null) {
            return new WriteCellData<>("");
        }
        
        // 使用NumberFormatUtil进行格式化
        String formattedValue = NumberFormatUtil.formatBigDecimal(value);
        return new WriteCellData<>(formattedValue);
    }
}
