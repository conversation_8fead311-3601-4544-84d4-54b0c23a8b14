# 资产负债表数字格式化修复

## 问题描述

在资产负债表的Excel导出功能中，存在以下两个数字格式化问题：

1. **零值显示问题**: 数值为0时显示为科学计数法 `0E-10`，应该显示为 `0`
2. **小数位过多问题**: 数值如 `669759905.3000000000` 显示了过多的无效小数位，应该显示为 `669759905.3`

## 修复方案

### 1. 后端修复 - BigDecimalConverter

修改了 `app/src/main/java/com/xl/alm/app/util/BigDecimalConverter.java` 文件：

```java
@Override
public WriteCellData<?> convertToExcelData(WriteConverterContext<BigDecimal> context) {
    BigDecimal value = context.getValue();
    if (value == null) {
        return new WriteCellData<>("");
    }
    
    // 处理零值，避免科学计数法显示
    if (value.compareTo(BigDecimal.ZERO) == 0) {
        return new WriteCellData<>("0");
    }
    
    // 去除不必要的尾随零，并避免科学计数法
    String formattedValue = value.stripTrailingZeros().toPlainString();
    
    return new WriteCellData<>(formattedValue);
}
```

**关键改进**:
- 使用 `value.compareTo(BigDecimal.ZERO) == 0` 检测零值
- 使用 `stripTrailingZeros()` 去除尾随零
- 使用 `toPlainString()` 避免科学计数法

### 2. DTO配置优化

在 `app/src/main/java/com/xl/alm/app/dto/BalanceSheetDTO.java` 中为BigDecimal字段添加了scale配置：

```java
@Excel(name = "期末余额金额", scale = 2)
@Excel(name = "年初余额金额", scale = 2)
```

### 3. 前端显示优化

在 `web/src/views/liab/balance/sheet/index.vue` 中：

1. **表格列显示优化**:
```vue
<el-table-column label="期末余额金额" align="center" prop="endingBalance">
  <template slot-scope="scope">
    {{ formatNumber(scope.row.endingBalance) }}
  </template>
</el-table-column>
```

2. **添加格式化方法**:
```javascript
formatNumber(value) {
  if (value === null || value === undefined || value === '') {
    return '';
  }
  
  const num = parseFloat(value);
  
  // 如果是0，直接返回'0'
  if (num === 0) {
    return '0';
  }
  
  // 如果是整数，不显示小数点
  if (num % 1 === 0) {
    return num.toString();
  }
  
  // 保留有效小数位，去除尾随零
  return num.toString().replace(/\.?0+$/, '');
}
```

## 测试用例

创建了 `app/src/test/java/com/xl/alm/app/util/BigDecimalConverterTest.java` 测试文件，包含以下测试场景：

- 零值测试: `BigDecimal.ZERO` → `"0"`
- 很小值测试: `0.0000000001` → `"0.0000000001"`
- 尾随零测试: `669759905.3000000000` → `"669759905.3"`
- 整数测试: `1000000000` → `"1000000000"`
- null值测试: `null` → `""`
- 负数测试: `-123.4500` → `"-123.45"`

## 修复效果

### 修复前
- 零值: `0E-10`
- 小数: `669759905.3000000000`

### 修复后
- 零值: `0`
- 小数: `669759905.3`

## 影响范围

此修复影响所有使用BigDecimal类型的Excel导出功能，确保数字格式化的一致性和可读性。

## 注意事项

1. 修复保持了数值的精度，只是改善了显示格式
2. 前端和后端都进行了相应的格式化处理
3. 测试用例确保了修复的正确性和稳定性
