# 资产负债表数字格式化修复

## 问题描述

在资产负债表的Excel导出功能中，存在以下两个数字格式化问题：

1. **零值显示问题**: 数值为0时显示为科学计数法 `0E-10`，应该显示为 `0`
2. **小数位过多问题**: 数值如 `669759905.3000000000` 显示了过多的无效小数位，应该显示为 `669759905.3`

## 修复方案

### 1. 新增数字格式化工具类 - NumberFormatUtil

创建了 `app/src/main/java/com/xl/alm/app/util/NumberFormatUtil.java` 工具类：

```java
public static String formatBigDecimal(BigDecimal value) {
    if (value == null) {
        return "";
    }

    // 处理零值，避免科学计数法显示
    if (value.compareTo(BigDecimal.ZERO) == 0) {
        return "0";
    }

    // 去除不必要的尾随零，并避免科学计数法
    return value.stripTrailingZeros().toPlainString();
}
```

**关键特性**:
- 使用 `value.compareTo(BigDecimal.ZERO) == 0` 检测零值
- 使用 `stripTrailingZeros()` 去除尾随零
- 使用 `toPlainString()` 避免科学计数法
- 支持多种数字类型的格式化

### 2. 新增专用BigDecimal转换器 - BalanceSheetBigDecimalConverter

创建了 `app/src/main/java/com/xl/alm/app/util/BalanceSheetBigDecimalConverter.java`：

```java
@Override
public WriteCellData<?> convertToExcelData(WriteConverterContext<BigDecimal> context) {
    BigDecimal value = context.getValue();
    if (value == null) {
        return new WriteCellData<>("");
    }

    // 使用NumberFormatUtil进行格式化
    String formattedValue = NumberFormatUtil.formatBigDecimal(value);
    return new WriteCellData<>(formattedValue);
}
```

### 3. 新增专用Excel工具类 - BalanceSheetExcelUtil

创建了 `app/src/main/java/com/xl/alm/app/util/BalanceSheetExcelUtil.java`：

- 专门用于资产负债表的Excel导入导出
- 自动注册BalanceSheetBigDecimalConverter
- 保持与原有ExcelUtil相同的接口

### 2. DTO配置优化

在 `app/src/main/java/com/xl/alm/app/dto/BalanceSheetDTO.java` 中为BigDecimal字段添加了scale配置：

```java
@Excel(name = "期末余额金额", scale = 2)
@Excel(name = "年初余额金额", scale = 2)
```

### 3. 前端显示优化

在 `web/src/views/liab/balance/sheet/index.vue` 中：

1. **表格列显示优化**:
```vue
<el-table-column label="期末余额金额" align="center" prop="endingBalance">
  <template slot-scope="scope">
    {{ formatNumber(scope.row.endingBalance) }}
  </template>
</el-table-column>
```

2. **添加格式化方法**:
```javascript
formatNumber(value) {
  if (value === null || value === undefined || value === '') {
    return '';
  }

  const num = parseFloat(value);

  // 如果是0，直接返回'0'
  if (num === 0) {
    return '0';
  }

  // 如果是整数，不显示小数点
  if (num % 1 === 0) {
    return num.toString();
  }

  // 保留有效小数位，去除尾随零
  return num.toString().replace(/\.?0+$/, '');
}
```

### 4. Controller修改

修改了 `app/src/main/java/com/xl/alm/app/controller/BalanceSheetController.java`：

- 将ExcelUtil替换为BalanceSheetExcelUtil
- 保持接口不变，只改变内部实现

## 新增文件清单

1. **NumberFormatUtil.java** - 数字格式化工具类
2. **BalanceSheetBigDecimalConverter.java** - 专用BigDecimal转换器
3. **BalanceSheetExcelUtil.java** - 专用Excel工具类
4. **NumberFormatUtilTest.java** - 测试用例

## 测试用例

创建了 `app/src/test/java/com/xl/alm/app/util/NumberFormatUtilTest.java` 测试文件，包含以下测试场景：

- 零值测试: `BigDecimal.ZERO` → `"0"`
- 很小值测试: `0.0000000001` → `"0.0000000001"`
- 尾随零测试: `669759905.3000000000` → `"669759905.3"`
- 整数测试: `1000000000` → `"1000000000"`
- null值测试: `null` → `""`
- 负数测试: `-123.4500` → `"-123.45"`
- 字符串格式化测试
- Object类型格式化测试

## 修复效果

### 修复前
- 零值: `0E-10`
- 小数: `669759905.3000000000`

### 修复后
- 零值: `0`
- 小数: `669759905.3`

## 影响范围

此修复仅影响资产负债表的Excel导入导出功能，不影响其他模块：

- **资产负债表**: 使用新的BalanceSheetExcelUtil和BalanceSheetBigDecimalConverter
- **其他模块**: 继续使用原有的ExcelUtil和BigDecimalConverter，保持不变

## 优势

1. **非侵入性**: 不修改原有的公共工具类，避免影响其他功能
2. **专用性**: 针对资产负债表的特殊需求进行优化
3. **可扩展性**: 其他模块如需类似功能，可以复用NumberFormatUtil
4. **向后兼容**: 保持原有接口不变，只改变内部实现

## 注意事项

1. 修复保持了数值的精度，只是改善了显示格式
2. 前端和后端都进行了相应的格式化处理
3. 测试用例确保了修复的正确性和稳定性
4. 原有的BigDecimalConverter和ExcelUtil保持不变，确保系统稳定性
