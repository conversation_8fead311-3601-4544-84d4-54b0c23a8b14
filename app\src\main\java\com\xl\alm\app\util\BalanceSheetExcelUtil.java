package com.xl.alm.app.util;

import com.alibaba.excel.EasyExcel;
import com.jd.lightning.common.exception.ServiceException;
import lombok.extern.slf4j.Slf4j;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.lang.reflect.Field;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 资产负债表专用Excel工具类
 * 解决BigDecimal格式化问题
 *
 * <AUTHOR> Assistant
 */
@Slf4j
public class BalanceSheetExcelUtil<T> {

    private Class<T> clazz;

    public BalanceSheetExcelUtil(Class<T> clazz) {
        this.clazz = clazz;
    }

    /**
     * 导出Excel
     *
     * @param list 数据列表
     * @param sheetName 工作表名称
     * @param response 响应对象
     */
    public void exportExcel(List<T> list, String sheetName, HttpServletResponse response) {
        try {
            // 处理数据，确保每个单元格的内容不超过32767字符
            List<T> processedList = new ArrayList<>();
            for (T item : list) {
                processedList.add(processLongFields(item));
            }

            String fileName = URLEncoder.encode(sheetName, "UTF-8");
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");

            // 使用专用的BigDecimal转换器
            EasyExcel.write(response.getOutputStream(), clazz)
                    .registerConverter(new MapConverter())
                    .registerConverter(new BalanceSheetBigDecimalConverter())
                    .excludeColumnFiledNames(getExcludeColumnFiledNames())
                    .sheet(sheetName)
                    .doWrite(processedList);
        } catch (Exception e) {
            log.error("导出Excel异常", e);
            throw new ServiceException("导出Excel失败: " + e.getMessage());
        }
    }

    /**
     * 导出模板
     *
     * @param response 响应对象
     * @param templateName 模板名称
     */
    public void exportTemplateExcel(HttpServletResponse response, String templateName) {
        try {
            // 创建一个空实例作为模板
            T instance = clazz.newInstance();
            List<T> templateList = new ArrayList<>();
            templateList.add(instance);

            // 处理数据，确保每个单元格的内容不超过32767字符
            List<T> processedList = new ArrayList<>();
            for (T item : templateList) {
                processedList.add(processLongFields(item));
            }

            String fileName = URLEncoder.encode(templateName + "模板", "UTF-8");
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");

            // 使用专用的BigDecimal转换器
            EasyExcel.write(response.getOutputStream(), clazz)
                    .registerConverter(new MapConverter())
                    .registerConverter(new BalanceSheetBigDecimalConverter())
                    .excludeColumnFiledNames(getExcludeColumnFiledNames())
                    .sheet(templateName + "模板")
                    .doWrite(processedList);
        } catch (Exception e) {
            log.error("导出模板异常", e);
            throw new ServiceException("导出模板失败: " + e.getMessage());
        }
    }

    /**
     * 导入Excel
     *
     * @param inputStream 输入流
     * @return 数据列表
     */
    public List<T> importExcel(InputStream inputStream) {
        try {
            return EasyExcel.read(inputStream)
                    .head(clazz)
                    .registerConverter(new BalanceSheetBigDecimalConverter())
                    .sheet()
                    .doReadSync();
        } catch (Exception e) {
            log.error("导入Excel异常", e);
            throw new ServiceException("导入Excel失败: " + e.getMessage());
        }
    }

    /**
     * 获取需要排除的字段名
     *
     * @return 需要排除的字段名列表
     */
    private Set<String> getExcludeColumnFiledNames() {
        Set<String> excludeColumnFiledNames = new HashSet<>();
        excludeColumnFiledNames.add("createBy");
        excludeColumnFiledNames.add("createTime");
        excludeColumnFiledNames.add("updateBy");
        excludeColumnFiledNames.add("updateTime");
        excludeColumnFiledNames.add("remark");
        excludeColumnFiledNames.add("isDel");
        return excludeColumnFiledNames;
    }

    /**
     * 处理长文本字段，确保不超过Excel单元格最大长度限制
     *
     * @param item 原始数据项
     * @return 处理后的数据项
     */
    @SuppressWarnings("unchecked")
    private T processLongFields(T item) {
        if (item == null) {
            return null;
        }

        try {
            // 创建一个新实例
            T newItem = (T) item.getClass().newInstance();

            // 获取所有字段
            Field[] fields = item.getClass().getDeclaredFields();

            for (Field field : fields) {
                field.setAccessible(true);

                // 跳过static和final字段
                int modifiers = field.getModifiers();
                if (java.lang.reflect.Modifier.isStatic(modifiers) || java.lang.reflect.Modifier.isFinal(modifiers)) {
                    continue;
                }

                Object value = field.get(item);

                // 如果是字符串类型且不为空
                if (value instanceof String) {
                    String strValue = (String) value;

                    // 如果超过32767字符（Excel单元格限制）
                    if (strValue.length() > 32767) {
                        // 截断并添加提示
                        String truncated = strValue.substring(0, 32700) + "... (内容过长已截断)";
                        field.set(newItem, truncated);
                    } else {
                        field.set(newItem, value);
                    }
                } else {
                    // 非字符串类型直接复制
                    field.set(newItem, value);
                }
            }

            return newItem;
        } catch (Exception e) {
            log.error("处理长文本字段异常", e);
            // 如果处理失败，返回原始项
            return item;
        }
    }
}
