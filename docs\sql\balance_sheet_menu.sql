-- 资产负债表菜单SQL
-- 负债产品信息管理主菜单 SQL
INSERT INTO sys_menu (menu_name,parent_id,order_num,path,component,query,is_frame,is_cache,menu_type,visible,status,perms,icon,create_by,create_time,update_by,update_time,remark) VALUES
('负债产品信息管理', 0, 30, 'liab', NULL, '', 1, 0, 'M', '0', '0', '', 'table', 'admin', SYSDATE(), '', NULL, '负债产品信息管理菜单');

-- 获取负债产品信息管理菜单ID
SET @liabMenuId = (SELECT menu_id FROM sys_menu WHERE menu_name = '负债产品信息管理' AND parent_id = 0 AND path = 'liab');

-- 资产负债表菜单 SQL (TB0002)
INSERT INTO sys_menu (menu_name,parent_id,order_num,path,component,query,is_frame,is_cache,menu_type,visible,status,perms,icon,create_by,create_time,update_by,update_time,remark) VALUES
('资产负债表管理', '2208', 1, 'balanceSheet', 'liab/balance/sheet/index', '', 1, 0, 'C', '0', '0', 'liab:balance:sheet:list', 'table', 'admin', SYSDATE(), '', NULL, '资产负债表管理菜单');

-- 按钮父菜单ID
SET @parentId = LAST_INSERT_ID();

-- 按钮 SQL
INSERT INTO sys_menu (menu_name,parent_id,order_num,path,component,query,is_frame,is_cache,menu_type,visible,status,perms,icon,create_by,create_time,update_by,update_time,remark) VALUES
('资产负债表查询', @parentId, 1, '#', '', '', 1, 0, 'F', '0', '0', 'liab:balance:sheet:query', '#', 'admin', SYSDATE(), '', NULL, ''),
('资产负债表新增', @parentId, 2, '#', '', '', 1, 0, 'F', '0', '0', 'liab:balance:sheet:add', '#', 'admin', SYSDATE(), '', NULL, ''),
('资产负债表修改', @parentId, 3, '#', '', '', 1, 0, 'F', '0', '0', 'liab:balance:sheet:edit', '#', 'admin', SYSDATE(), '', NULL, ''),
('资产负债表删除', @parentId, 4, '#', '', '', 1, 0, 'F', '0', '0', 'liab:balance:sheet:remove', '#', 'admin', SYSDATE(), '', NULL, ''),
('资产负债表导出', @parentId, 5, '#', '', '', 1, 0, 'F', '0', '0', 'liab:balance:sheet:export', '#', 'admin', SYSDATE(), '', NULL, ''),
('资产负债表导入', @parentId, 6, '#', '', '', 1, 0, 'F', '0', '0', 'liab:balance:sheet:import', '#', 'admin', SYSDATE(), '', NULL, '');
