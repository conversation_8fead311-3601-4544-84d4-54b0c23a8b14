package com.xl.alm.app.util;

import java.math.BigDecimal;

/**
 * 数字格式化工具类
 * 用于解决BigDecimal在Excel导出和前端显示时的格式化问题
 *
 * <AUTHOR> Assistant
 */
public class NumberFormatUtil {

    /**
     * 格式化BigDecimal为字符串，解决零值科学计数法和尾随零问题
     * 
     * @param value BigDecimal值
     * @return 格式化后的字符串
     */
    public static String formatBigDecimal(BigDecimal value) {
        if (value == null) {
            return "";
        }
        
        // 处理零值，避免科学计数法显示
        if (value.compareTo(BigDecimal.ZERO) == 0) {
            return "0";
        }
        
        // 去除不必要的尾随零，并避免科学计数法
        return value.stripTrailingZeros().toPlainString();
    }

    /**
     * 格式化数字字符串，去除尾随零
     * 
     * @param numberStr 数字字符串
     * @return 格式化后的字符串
     */
    public static String formatNumberString(String numberStr) {
        if (numberStr == null || numberStr.trim().isEmpty()) {
            return "";
        }
        
        try {
            BigDecimal value = new BigDecimal(numberStr);
            return formatBigDecimal(value);
        } catch (NumberFormatException e) {
            // 如果不是有效数字，返回原字符串
            return numberStr;
        }
    }

    /**
     * 格式化Object类型的数字，支持多种数字类型
     * 
     * @param value 数字对象
     * @return 格式化后的字符串
     */
    public static String formatNumber(Object value) {
        if (value == null) {
            return "";
        }
        
        if (value instanceof BigDecimal) {
            return formatBigDecimal((BigDecimal) value);
        } else if (value instanceof Double) {
            Double doubleValue = (Double) value;
            if (doubleValue == 0.0) {
                return "0";
            }
            return formatBigDecimal(BigDecimal.valueOf(doubleValue));
        } else if (value instanceof Float) {
            Float floatValue = (Float) value;
            if (floatValue == 0.0f) {
                return "0";
            }
            return formatBigDecimal(BigDecimal.valueOf(floatValue.doubleValue()));
        } else if (value instanceof Long || value instanceof Integer) {
            return value.toString();
        } else if (value instanceof String) {
            return formatNumberString((String) value);
        } else {
            return value.toString();
        }
    }

    /**
     * 检查数字是否为零
     * 
     * @param value 数字值
     * @return 是否为零
     */
    public static boolean isZero(BigDecimal value) {
        return value != null && value.compareTo(BigDecimal.ZERO) == 0;
    }

    /**
     * 检查数字是否为整数
     * 
     * @param value BigDecimal值
     * @return 是否为整数
     */
    public static boolean isInteger(BigDecimal value) {
        if (value == null) {
            return false;
        }
        return value.stripTrailingZeros().scale() <= 0;
    }

    /**
     * 格式化BigDecimal为指定小数位数的字符串
     * 
     * @param value BigDecimal值
     * @param scale 小数位数
     * @return 格式化后的字符串
     */
    public static String formatBigDecimalWithScale(BigDecimal value, int scale) {
        if (value == null) {
            return "";
        }
        
        if (value.compareTo(BigDecimal.ZERO) == 0) {
            return "0";
        }
        
        // 设置小数位数并去除尾随零
        BigDecimal scaledValue = value.setScale(scale, BigDecimal.ROUND_HALF_UP);
        return scaledValue.stripTrailingZeros().toPlainString();
    }
}
