package com.xl.alm.app.service;

import com.xl.alm.app.dto.BalanceSheetDTO;
import com.xl.alm.app.entity.BalanceSheetEntity;
import com.xl.alm.app.query.BalanceSheetQuery;

import java.util.List;

/**
 * 资产负债表 Service 接口
 *
 * <AUTHOR> Assistant
 */
public interface BalanceSheetService {

    /**
     * 查询资产负债表列表
     *
     * @param balanceSheetQuery 资产负债表查询条件
     * @return 资产负债表列表
     */
    List<BalanceSheetDTO> selectBalanceSheetDtoList(BalanceSheetQuery balanceSheetQuery);

    /**
     * 用id查询资产负债表
     *
     * @param id id
     * @return 资产负债表
     */
    BalanceSheetDTO selectBalanceSheetDtoById(Long id);

    /**
     * 根据账期、类别和项目名称查询资产负债表
     *
     * @param accountingPeriod 账期
     * @param category         类别
     * @param itemName         项目名称
     * @return 资产负债表
     */
    BalanceSheetDTO selectBalanceSheetDtoByCondition(
            String accountingPeriod,
            String category,
            String itemName);

    /**
     * 新增资产负债表
     *
     * @param dto 资产负债表
     * @return 结果
     */
    int addBalanceSheetDto(BalanceSheetDTO dto);

    /**
     * 修改资产负债表
     *
     * @param dto 资产负债表
     * @return 结果
     */
    int updateBalanceSheetDto(BalanceSheetDTO dto);

    /**
     * 批量删除资产负债表
     *
     * @param ids 需要删除的资产负债表主键集合
     * @return 结果
     */
    int deleteBalanceSheetDtoByIds(Long[] ids);

    /**
     * 删除资产负债表信息
     *
     * @param id 资产负债表主键
     * @return 结果
     */
    int deleteBalanceSheetDtoById(Long id);

    /**
     * 批量插入资产负债表数据
     *
     * @param balanceSheetDtoList 资产负债表列表
     * @return 影响行数
     */
    int batchInsertBalanceSheetDto(List<BalanceSheetDTO> balanceSheetDtoList);

    /**
     * 删除指定账期的资产负债表数据
     *
     * @param accountingPeriod 账期
     * @return 影响行数
     */
    int deleteBalanceSheetDtoByPeriod(String accountingPeriod);

    /**
     * 导入资产负债表
     *
     * @param dtoList       资产负债表数据列表
     * @param updateSupport 是否更新支持，如果已存在，是否更新
     * @param username      操作用户
     * @return 结果
     */
    String importBalanceSheetDto(List<BalanceSheetDTO> dtoList, Boolean updateSupport, String username);
}
