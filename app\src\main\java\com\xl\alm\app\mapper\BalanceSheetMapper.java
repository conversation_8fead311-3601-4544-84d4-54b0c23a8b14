package com.xl.alm.app.mapper;

import com.xl.alm.app.dto.BalanceSheetDTO;
import com.xl.alm.app.entity.BalanceSheetEntity;
import com.xl.alm.app.query.BalanceSheetQuery;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 资产负债表 Mapper 接口
 *
 * <AUTHOR> Assistant
 */
@Mapper
public interface BalanceSheetMapper {

    /**
     * 查询资产负债表列表
     *
     * @param balanceSheetQuery 资产负债表查询条件
     * @return 资产负债表列表
     */
    List<BalanceSheetEntity> selectBalanceSheetEntityList(BalanceSheetQuery balanceSheetQuery);

    /**
     * 用id查询资产负债表
     *
     * @param id id
     * @return 资产负债表
     */
    BalanceSheetEntity selectBalanceSheetEntityById(Long id);

    /**
     * 根据账期、类别和项目名称查询资产负债表
     *
     * @param accountingPeriod 账期
     * @param category 类别
     * @param itemName 项目名称
     * @return 资产负债表
     */
    BalanceSheetEntity selectBalanceSheetEntityByCondition(
            @Param("accountingPeriod") String accountingPeriod,
            @Param("category") String category,
            @Param("itemName") String itemName);

    /**
     * 新增资产负债表
     *
     * @param balanceSheetEntity 资产负债表
     * @return 结果
     */
    int insertBalanceSheetEntity(BalanceSheetEntity balanceSheetEntity);

    /**
     * 修改资产负债表
     *
     * @param balanceSheetEntity 资产负债表
     * @return 结果
     */
    int updateBalanceSheetEntity(BalanceSheetEntity balanceSheetEntity);

    /**
     * 删除资产负债表
     *
     * @param id 资产负债表主键
     * @return 结果
     */
    int deleteBalanceSheetEntityById(Long id);

    /**
     * 批量删除资产负债表
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteBalanceSheetEntityByIds(Long[] ids);

    /**
     * 批量插入资产负债表数据
     *
     * @param balanceSheetEntityList 资产负债表列表
     * @return 影响行数
     */
    int batchInsertBalanceSheetEntity(List<BalanceSheetEntity> balanceSheetEntityList);

    /**
     * 删除指定账期的资产负债表数据
     *
     * @param accountingPeriod 账期
     * @return 影响行数
     */
    int deleteBalanceSheetEntityByPeriod(String accountingPeriod);
}
